# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Jittor cache
.cache/
/root/.cache/jittor/

# C extensions
*.so
*.pyd
datasets/
# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
pytestdebug.log

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# Jupyter Notebook
.ipynb_checkpoints

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static analyzer
.pytype/

# environments
.env
.venv
env/
venv/
ENV/

# DFormer-Jittor specific
# ----------------------------------------------------------------
# Datasets - typically large and not versioned
datasets/
/datasets/
*.zip
*.tar.gz

# Checkpoints and logs
checkpoints/
/checkpoints/
*.log
log.txt
val_last.log
log_last.log
*.pth
*.pkl

# IDE and OS files
.vscode/
.idea/
*.swp
*~
.DS_Store
Thumbs.db

# Output directories
output/
/output/
results/
/results/
figs/

trash/
/trash/
# User-uploaded files (if any)
uploads/

# Temporary files
tmp/
/tmp/
temp/
/temp/ 

DFormer/
/DFormer/