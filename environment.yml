name: jittordet
channels:
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch
  - pytorch
  - nvidia
  - conda-forge
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/pro
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/r
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/msys2
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - blas=1.0=mkl
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.1.31=hbcca054_0
  - certifi=2024.8.30=py38h06a4308_0
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - cuda-cudart=11.7.99=0
  - cuda-cupti=11.7.101=0
  - cuda-libraries=11.7.1=0
  - cuda-nvrtc=11.7.99=0
  - cuda-nvtx=11.7.91=0
  - cuda-runtime=11.7.1=0
  - cudatoolkit=11.3.1=h9edb442_10
  - ffmpeg=4.3=hf484d3e_0
  - flit-core=3.6.0=pyhd3eb1b0_0
  - freetype=2.12.1=h4a9f257_0
  - giflib=5.2.2=h5eee18b_0
  - gmp=6.2.1=h295c915_3
  - gnutls=3.6.15=he1e5248_0
  - intel-openmp=2021.4.0=h06a4308_3561
  - jpeg=9e=h5eee18b_3
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - lerc=3.0=h295c915_0
  - libcublas=**********=0
  - libcufft=**********=h4fbf590_0
  - libcufile=*******=0
  - libcurand=**********=0
  - libcusolver=********=0
  - libcusparse=*********=0
  - libdeflate=1.8=h7f8727e_5
  - libedit=3.1.20221030=h5eee18b_0
  - libffi=3.2.1=hf484d3e_1007
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h5eee18b_3
  - libidn2=2.3.4=h5eee18b_0
  - libnpp=11.7.4.75=0
  - libnvjpeg=11.8.0.2=0
  - libpng=1.6.39=h5eee18b_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.19.0=h5eee18b_0
  - libtiff=4.5.0=hecacb30_0
  - libunistring=0.9.10=h27cfd23_0
  - libuv=1.43.0=h7f98852_0
  - libwebp=1.2.4=h11a3e52_1
  - libwebp-base=1.2.4=h5eee18b_1
  - lz4-c=1.9.4=h6a678d5_1
  - mkl=2021.4.0=h06a4308_640
  - mkl-service=2.4.0=py38h7f8727e_0
  - mkl_fft=1.3.1=py38hd3c417c_0
  - mkl_random=1.2.2=py38h51133e4_0
  - ncurses=6.3=h5eee18b_3
  - nettle=3.7.3=hbbd107a_1
  - openh264=2.1.1=h4ff587b_0
  - openssl=1.1.1w=h7f8727e_0
  - pillow=9.4.0=py38h6a678d5_0
  - pip=24.2=py38h06a4308_0
  - pycparser=2.21=pyhd3eb1b0_0
  - python=3.8.2=h191fe78_0
  - pytorch=1.10.1=py3.8_cuda11.3_cudnn8.2.0_0
  - pytorch-cuda=11.7=h778d358_5
  - pytorch-mutex=1.0=cuda
  - readline=7.0=h7b6447c_5
  - setuptools=75.1.0=py38h06a4308_0
  - six=1.16.0=pyhd3eb1b0_1
  - sqlite=3.33.0=h62c20be_0
  - tk=8.6.12=h1ccaba5_0
  - torchaudio=0.10.1=py38_cu113
  - torchvision=0.11.2=py38_cu113
  - typing_extensions=4.11.0=py38h06a4308_0
  - wheel=0.37.1=pyhd3eb1b0_0
  - xz=5.2.8=h5eee18b_0
  - zlib=1.2.13=h5eee18b_0
  - zstd=1.5.2=ha4553b6_0
  - pip:
      - addict==2.4.0
      - astunparse==1.6.3
      - cfgv==3.4.0
      - contourpy==1.1.1
      - cycler==0.12.1
      - distlib==0.3.9
      - easydict==1.13
      - filelock==3.16.1
      - fonttools==4.57.0
      - identify==2.6.1
      - importlib-resources==6.4.5
      - jittor==********
      - kiwisolver==1.4.7
      - matplotlib==3.7.5
      - nodeenv==1.9.1
      - numpy==1.23.2
      - opencv-python==*********
      - packaging==25.0
      - platformdirs==4.3.6
      - pre-commit==3.5.0
      - pycocotools==2.0.7
      - pyparsing==3.1.4
      - pyyaml==6.0.2
      - terminaltables==3.1.10
      - tqdm==4.67.1
      - virtualenv==20.29.2
      - zipp==3.20.2
prefix: /home/<USER>/anaconda3/envs/jittordet
