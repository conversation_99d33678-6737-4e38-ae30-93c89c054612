import jittor as jt
import argparse
import os

def inspect_checkpoint(checkpoint_path):
    """
    Loads a Jittor checkpoint and prints its contents.
    
    Args:
        checkpoint_path (str): The path to the checkpoint file (.pkl).
    """
    if not os.path.exists(checkpoint_path):
        print(f"Error: Checkpoint file not found at '{checkpoint_path}'")
        return

    try:
        # Jittor models are often saved with jt.save, which uses pickle.
        # We can load them with jt.load.
        checkpoint = jt.load(checkpoint_path)
        print(f"Successfully loaded checkpoint: {checkpoint_path}\n")

        if not isinstance(checkpoint, dict):
            print("Checkpoint is not a dictionary. Printing its type and content:")
            print(f"Type: {type(checkpoint)}")
            print(f"Content: {checkpoint}")
            return

        print("Checkpoint contains the following keys:\n" + "="*40)
        
        for key, value in checkpoint.items():
            print(f"\nKey: '{key}'")
            if isinstance(value, jt.Var):
                print(f"  - Type:  Jittor Var")
                print(f"  - Shape: {value.shape}")
                print(f"  - Dtype: {value.dtype}")
            else:
                print(f"  - Type:  {type(value).__name__}")
                # Print the value itself if it's a simple type, otherwise just show the type.
                if isinstance(value, (int, float, str, bool, list, tuple, dict)):
                     # Truncate long lists/tuples/dicts for readability
                    value_repr = repr(value)
                    if len(value_repr) > 150:
                        value_repr = value_repr[:150] + '...'
                    print(f"  - Value: {value_repr}")
        
        print("\n" + "="*40 + "\nInspection complete.")

    except Exception as e:
        print(f"An error occurred while reading the checkpoint: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Inspect a Jittor model checkpoint file.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        '--checkpoint',
        type=str,
        required=True,
        help="Path to the checkpoint file (.pkl) to inspect."
    )
    
    args = parser.parse_args()
    inspect_checkpoint(args.checkpoint) 