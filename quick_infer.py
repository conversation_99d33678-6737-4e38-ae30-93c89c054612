#!/usr/bin/env python3
"""
Quick inference script for DFormer Jittor implementation
This script provides fast single-scale inference without augmentations
"""

import argparse
import importlib
import os
import sys
import time
from importlib import import_module

import numpy as np
import jittor as jt
from jittor import nn

# Add project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from models.builder import EncoderDecoder as segmodel
from utils.dataloader.dataloader import get_val_loader
from utils.dataloader.RGBXDataset import RGBXDataset
from utils.engine.engine import Engine
from utils.engine.logger import get_logger
from utils.metric import SegmentationMetric

logger = get_logger()

parser = argparse.ArgumentParser()
parser.add_argument("--config", help="train config file path", required=True)
parser.add_argument("--gpus", help="used gpu number", default="1")
parser.add_argument("--continue_fpath", help="checkpoint path", required=True)
parser.add_argument("--save_path", default=None, help="path to save predictions")
parser.add_argument("--batch_size", type=int, default=1, help="batch size for inference")

def convert_pytorch_keys_to_jittor(pytorch_state_dict):
    """Convert PyTorch checkpoint keys to Jittor model keys."""
    jittor_state_dict = {}
    
    for k, v in pytorch_state_dict.items():
        new_key = k
        
        # Convert PyTorch tensor to numpy array for Jittor
        if hasattr(v, 'detach'):
            v = v.detach().cpu().numpy()
        
        # Key mappings for decode_head
        if 'decode_head.conv_seg' in k:
            new_key = k.replace('decode_head.conv_seg', 'decode_head.cls_seg')
        elif 'decode_head.squeeze.bn' in k:
            new_key = k.replace('decode_head.squeeze.bn', 'decode_head.squeeze.norm')
        elif 'decode_head.hamburger.ham_out.bn' in k:
            new_key = k.replace('decode_head.hamburger.ham_out.bn', 'decode_head.hamburger.ham_out.norm')
        elif 'decode_head.align.bn' in k:
            new_key = k.replace('decode_head.align.bn', 'decode_head.align.norm')
        
        # Skip num_batches_tracked keys as they are not needed in Jittor
        if 'num_batches_tracked' in k:
            continue
            
        # Skip backbone norm keys that don't exist in Jittor model
        if any(x in k for x in ['backbone.norm0', 'backbone.norm1', 'backbone.norm2', 'backbone.norm3']):
            continue
        
        jittor_state_dict[new_key] = v
    
    return jittor_state_dict

def load_checkpoint(model, checkpoint_path):
    """Load PyTorch checkpoint into Jittor model."""
    logger.info(f"Loading checkpoint from {checkpoint_path}")
    
    import torch
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # Extract state dict
    if 'model' in checkpoint:
        weight = checkpoint['model']
    else:
        weight = checkpoint

    # Convert PyTorch keys to Jittor keys
    converted_weight = convert_pytorch_keys_to_jittor(weight)
    
    # Get model state dict
    model_dict = model.state_dict()
    
    # Filter and convert weights
    jittor_load_dict = {}
    for k, v in converted_weight.items():
        if k in model_dict:
            if isinstance(v, np.ndarray):
                jittor_load_dict[k] = jt.array(v)
            else:
                jittor_load_dict[k] = v
    
    # Load parameters
    model.load_parameters(jittor_load_dict)
    
    missing_keys = [k for k in model_dict.keys() if k not in converted_weight]
    unexpected_keys = [k for k in converted_weight.keys() if k not in model_dict]
    
    if missing_keys:
        logger.warning(f"Missing keys: {len(missing_keys)} keys")
    if unexpected_keys:
        logger.warning(f"Unexpected keys: {len(unexpected_keys)} keys")
    
    logger.info("Model weights loaded successfully")

def quick_evaluate(model, data_loader, num_classes):
    """Quick evaluation with single scale and no augmentation."""
    model.eval()
    
    metric = SegmentationMetric(num_classes)
    
    logger.info(f"Starting quick evaluation with {len(data_loader)} batches...")
    
    start_time = time.time()
    processed_batches = 0
    
    with jt.no_grad():
        for i, minibatch in enumerate(data_loader):
            try:
                rgb = minibatch['data']
                targets = minibatch['label']
                modal = minibatch.get('modal_x', None)
                
                # Forward pass
                if modal is not None:
                    output = model(rgb, modal)
                else:
                    output = model(rgb)
                
                if isinstance(output, dict):
                    output = output['out']
                
                # Get predictions
                predictions = jt.argmax(output, 1)[0]  # Jittor argmax returns tuple
                
                # predictions is already extracted from tuple above
                
                # Convert to numpy for metric calculation
                pred_numpy = predictions.numpy()
                target_numpy = targets.numpy()
                
                # Update metrics
                metric.update(pred_numpy, target_numpy)
                
                processed_batches += 1
                
                # Print progress every 50 batches
                if (i + 1) % 50 == 0:
                    elapsed = time.time() - start_time
                    speed = processed_batches / elapsed
                    logger.info(f"Processed {i+1}/{len(data_loader)} batches, Speed: {speed:.2f} batches/sec")
                
                # Clean memory every 20 batches
                if (i + 1) % 20 == 0:
                    jt.clean()
                    
            except Exception as e:
                logger.error(f"Error processing batch {i}: {e}")
                continue
    
    total_time = time.time() - start_time
    logger.info(f"Evaluation completed in {total_time:.2f} seconds")
    logger.info(f"Average speed: {processed_batches/total_time:.2f} batches/sec")
    
    return metric

def main():
    args = parser.parse_args()
    
    # Enable CUDA if available
    if jt.has_cuda:
        jt.flags.use_cuda = 1
        logger.info("Using CUDA")
    else:
        jt.flags.use_cuda = 0
        logger.warning("CUDA not available, using CPU")
    
    # Load config
    config = getattr(import_module(args.config), "C")
    config.pad = False  # No padding for inference
    
    if "x_modal" not in config:
        config["x_modal"] = "d"
    
    # Create engine
    with Engine(custom_parser=parser) as engine:
        # Get data loader
        val_loader, val_sampler = get_val_loader(engine, RGBXDataset, config, int(args.gpus))
        logger.info(f"Dataset size: {len(val_loader)} batches")
        
        # Build model
        BatchNorm2d = nn.BatchNorm2d
        model = segmodel(cfg=config, norm_layer=BatchNorm2d)
        
        # Load checkpoint
        load_checkpoint(model, args.continue_fpath)
        
        # Move to GPU if available
        if jt.has_cuda:
            model.cuda()
        
        # Run evaluation
        metric = quick_evaluate(model, val_loader, config.num_classes)
        
        # Compute and print results
        ious, miou = metric.compute_iou()
        acc, macc = metric.compute_pixel_acc()
        f1, mf1 = metric.compute_f1()
        
        logger.info("=" * 50)
        logger.info("EVALUATION RESULTS")
        logger.info("=" * 50)
        logger.info(f"Mean IoU: {miou:.2f}%")
        logger.info(f"Mean Accuracy: {macc:.2f}%")
        logger.info(f"Mean F1: {mf1:.2f}%")
        logger.info("=" * 50)

if __name__ == "__main__":
    main()
