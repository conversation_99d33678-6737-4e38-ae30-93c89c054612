model:
  type: RPNFramework
  preprocessor:
    type: Preprocessor
    mean: [123.675, 116.28, 103.53]
    std: [58.395, 57.12, 57.375]
    bgr_to_rgb: true
    pad_size_divisor: 32
  backbone:
    type: ResNet
    depth: 50
    frozen_stages: 1
    norm_eval: true
    return_stages: ['layer1', 'layer2', 'layer3', 'layer4']
    pretrained: 'jittorhub://resnet50.pkl'
  neck:
    type: FPN
    in_channels: [256, 512, 1024, 2048]
    out_channels: 256
    num_outs: 5
  rpn_head:
    type: RPNHead
    num_classes: 1
    in_channels: 256
    feat_channels: 256
    anchor_generator:
      type: AnchorGenerator
      scales: [8]
      ratios: [0.5, 1.0, 2.0]
      strides: [4, 8, 16, 32, 64]
    bbox_coder:
      type: DeltaXYWHBBoxCoder
      target_means: [.0, .0, .0, .0]
      target_stds: [1.0, 1.0, 1.0, 1.0]
    loss_cls:
      type: CrossEntropyLoss
      use_sigmoid: true
      loss_weight: 1.0
    loss_bbox:
      type: L1Loss
      loss_weight: 1.0
  train_cfg:
    rpn:
      assigner:
        type: MaxIoUAssigner
        pos_iou_thr: 0.7
        neg_iou_thr: 0.3
        min_pos_iou: 0.3
        match_low_quality: true
        ignore_iof_thr: -1
      sampler:
        type: RandomSampler
        num: 256
        pos_fraction: 0.5
        neg_pos_ub: -1
        add_gt_as_proposals: false
      allowed_border: -1
      pos_weight: -1
  test_cfg:
    rpn:
      nms_pre: 1000
      max_per_img: 1000
      nms:
        type: nms
        thresh: 0.7
      min_bbox_size: 0
